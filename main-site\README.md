# Proxy Network - Main Site

This is the main control site for the PHP proxy network system. It provides a user interface for browsing available proxy servers and connecting to them.

## Features

- **Server Listing**: Displays all available proxy servers with status and load information
- **Multi-language Support**: Supports multiple languages with easy switching
- **Responsive Design**: Works on desktop and mobile devices using Bootstrap 5
- **Ad Management**: Configurable ad placements in header, footer, and sidebar
- **Security**: Basic security measures including user agent filtering
- **No Database**: Uses flat configuration files for easy setup

## Installation

1. **Upload Files**: Upload all files to your main domain's web root
2. **Install Dependencies**: Run `composer install` in the project root
3. **Configure**: Edit the configuration files in `src/Config/` and `src/Servers/`
4. **Set Permissions**: Ensure the web server has read access to all files

## Configuration

### Main Configuration (`src/Config/config.php`)

```php
return [
    'site' => [
        'title' => 'Proxy Network',
        'description' => 'Access the web through our secure proxy network',
        'keywords' => 'proxy, web proxy, secure browsing, privacy',
        'url' => 'https://your-main-domain.com'
    ],
    'ads' => [
        'header' => '',
        'footer' => '',
        'sidebar' => ''
    ],
    'languages' => [
        'en' => 'English',
        'es' => 'Español',
        // Add more languages as needed
    ],
    'default_language' => 'en'
];
```

### Servers Configuration (`src/Servers/servers.php`)

```php
return [
    [
        'id' => 'server1',
        'name' => 'US East Server',
        'location' => 'United States (East)',
        'flag' => '🇺🇸',
        'url' => 'https://proxy1.yourdomain.com',
        'status' => 'online',
        'load' => 25,
        'description' => 'High-speed proxy server'
    ],
    // Add more servers as needed
];
```

### Adding Languages

1. Create a new language file in `lang/` directory (e.g., `fr.php`)
2. Copy the structure from `en.php` and translate the strings
3. Add the language to the configuration file

## Usage

1. Users visit the main site and see a list of available proxy servers
2. They can view server details including status and load
3. Clicking "Connect" prompts for a target URL and redirects to the proxy
4. The proxy script handles the actual web browsing

## Security Considerations

- User agent filtering is enabled by default
- Input sanitization is performed on all user inputs
- CSRF protection is available (enable in config)
- Rate limiting can be configured to prevent abuse

## File Structure

```
php-proxy-main/
├── composer.json           # Composer dependencies
├── public/
│   └── index.php          # Main entry point
├── src/
│   ├── Config/
│   │   └── config.php     # Main configuration
│   ├── Servers/
│   │   └── servers.php    # Server list
│   └── Templates/
│       └── template.php   # Main template
├── lang/
│   ├── en.php            # English translations
│   └── es.php            # Spanish translations
└── README.md             # This file
```

## Requirements

- PHP 7.4 or higher
- Composer
- Web server (Apache/Nginx)
- SSL certificate (recommended)

## Support

For issues and questions, please refer to the project documentation or create an issue in the project repository.