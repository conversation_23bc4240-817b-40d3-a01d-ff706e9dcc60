<!DOCTYPE html>
<html lang="{{ lang }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ site_title }}</title>
    <meta name="description" content="{{ site_description }}">
    <meta name="keywords" content="{{ site_keywords }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <style>
        .server-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
        }
        .server-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .status-maintenance { color: #ffc107; }
        .load-bar {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
            overflow: hidden;
        }
        .load-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .load-low { background-color: #28a745; }
        .load-medium { background-color: #ffc107; }
        .load-high { background-color: #dc3545; }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .language-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
    
    {{ custom_css }}
</head>
<body>
    <!-- Language Selector -->
    <div class="language-selector">
        <div class="dropdown">
            <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-globe"></i> {{ current_language }}
            </button>
            <ul class="dropdown-menu">
                {% for code, name in languages %}
                <li><a class="dropdown-item" href="?lang={{ code }}">{{ name }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Header Ads -->
    {{ header_ads }}

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-shield-check"></i> {{ site_title }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">{{ nav_home }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#servers">{{ nav_servers }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings">{{ nav_settings }}</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">{{ welcome_title }}</h1>
            <p class="lead mb-5">{{ welcome_subtitle }}</p>
            <a href="#servers" class="btn btn-light btn-lg">
                <i class="bi bi-arrow-down-circle"></i> {{ select_server }}
            </a>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container my-5">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer Ads -->
    {{ footer_ads }}

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">{{ footer_copyright|replace({'{year}': '2024'}) }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light me-3">{{ footer_privacy }}</a>
                    <a href="#" class="text-light me-3">{{ footer_terms }}</a>
                    <a href="#" class="text-light">{{ footer_contact }}</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Simple form validation and interaction
        document.addEventListener('DOMContentLoaded', function() {
            // Handle server selection
            const connectButtons = document.querySelectorAll('.connect-btn');
            connectButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const serverUrl = this.dataset.url;
                    const targetUrl = prompt('Enter the URL you want to visit:', 'https://example.com');
                    
                    if (targetUrl) {
                        try {
                            new URL(targetUrl); // Validate URL
                            window.location.href = serverUrl + '?url=' + encodeURIComponent(targetUrl);
                        } catch (e) {
                            alert('Please enter a valid URL');
                        }
                    }
                });
            });

            // Handle settings form
            const settingsForm = document.getElementById('settingsForm');
            if (settingsForm) {
                settingsForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('{{ settings_saved }}');
                });
            }
        });
    </script>
</body>
</html>