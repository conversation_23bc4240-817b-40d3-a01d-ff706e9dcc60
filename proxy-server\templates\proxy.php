<!DOCTYPE html>
<html lang="{{ lang }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - {{ site_title }}</title>
    <meta name="description" content="{{ description }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <style>
        .proxy-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .url-form {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
        }
        .proxy-content {
            min-height: 60vh;
            padding: 2rem 0;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .proxy-footer {
            background: #f8f9fa;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        .settings-panel {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .language-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
        }
        .proxy-info {
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        .ad-placeholder {
            background: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            text-align: center;
            color: #6c757d;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Language Selector -->
    <div class="language-selector">
        <div class="dropdown">
            <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-globe"></i> {{ current_language }}
            </button>
            <ul class="dropdown-menu">
                {% for code, name in languages %}
                <li><a class="dropdown-item" href="?lang={{ code }}">{{ name }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Header Ads -->
    {{ header_ads }}

    <!-- Proxy Header -->
    <header class="proxy-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-shield-check"></i> {{ site_title }}
                    </h1>
                </div>
                <div class="col-md-6">
                    <form class="url-form" id="proxyForm" method="get">
                        <div class="input-group">
                            <input type="url" class="form-control" name="url" 
                                   placeholder="{{ enter_url }}" value="{{ current_url }}"
                                   required>
                            <button class="btn btn-light" type="submit">
                                <i class="bi bi-arrow-right-circle"></i> {{ go_button }}
                            </button>
                        </div>
                    </form>
                </div>
                <div class="col-md-3 text-end">
                    <div class="proxy-info">
                        <i class="bi bi-server"></i> {{ browsing_via }}
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#proxyNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="proxyNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house-door"></i> {{ nav_home }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=settings">
                            <i class="bi bi-gear"></i> {{ nav_settings }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=about">
                            <i class="bi bi-info-circle"></i> {{ nav_about }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=contact">
                            <i class="bi bi-envelope"></i> {{ nav_contact }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="proxy-content">
        <div class="container">
            <!-- Sidebar Ads -->
            <div class="row">
                <div class="col-md-3">
                    {{ sidebar_ads }}
                    
                    <!-- Quick Settings -->
                    {% if show_quick_settings %}
                    <div class="settings-panel">
                        <h5 class="mb-3">
                            <i class="bi bi-lightning"></i> Quick Settings
                        </h5>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="quickJs" 
                                   {% if settings.javascript %}checked{% endif %}>
                            <label class="form-check-label" for="quickJs">
                                {{ setting_javascript }}
                            </label>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="quickCookies" 
                                   {% if settings.cookies %}checked{% endif %}>
                            <label class="form-check-label" for="quickCookies">
                                {{ setting_cookies }}
                            </label>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="quickImages" 
                                   {% if settings.images %}checked{% endif %}>
                            <label class="form-check-label" for="quickImages">
                                {{ setting_images }}
                            </label>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Main Content Area -->
                <div class="col-md-9">
                    <!-- Loading Spinner -->
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ status_loading }}</span>
                        </div>
                        <p class="mt-2">{{ status_loading }}</p>
                    </div>
                    
                    <!-- Content Area -->
                    <div id="contentArea">
                        {% block content %}{% endblock %}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer Ads -->
    {{ footer_ads }}

    <!-- Footer -->
    <footer class="proxy-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">{{ footer_copyright|replace({'{year}': '2024', '{server_name}': server_name}) }}</p>
                    <p class="small text-muted mb-0">{{ footer_disclaimer }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-decoration-none me-3">{{ footer_privacy }}</a>
                    <a href="#" class="text-decoration-none">{{ footer_terms }}</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Proxy functionality
        document.addEventListener('DOMContentLoaded', function() {
            const proxyForm = document.getElementById('proxyForm');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const contentArea = document.getElementById('contentArea');
            
            if (proxyForm) {
                proxyForm.addEventListener('submit', function(e) {
                    const url = this.url.value.trim();
                    if (url) {
                        try {
                            new URL(url); // Validate URL
                            loadingSpinner.style.display = 'block';
                            contentArea.style.opacity = '0.5';
                        } catch (err) {
                            e.preventDefault();
                            alert('{{ error_invalid_url }}');
                        }
                    }
                });
            }
            
            // Quick settings
            const quickSettings = ['quickJs', 'quickCookies', 'quickImages'];
            quickSettings.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', function() {
                        // Save setting via AJAX or form submission
                        const setting = id.replace('quick', '').toLowerCase();
                        const value = this.checked ? '1' : '0';
                        
                        // Simple implementation - reload with new settings
                        const url = new URL(window.location);
                        url.searchParams.set(setting, value);
                        window.location.href = url.toString();
                    });
                }
            });
            
            // Auto-hide alerts
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }, 5000);
            });
        });
        
        // Security functions
        function sanitizeUrl(url) {
            try {
                const urlObj = new URL(url);
                // Remove potentially dangerous parts
                urlObj.username = '';
                urlObj.password = '';
                return urlObj.toString();
            } catch (e) {
                return url;
            }
        }
        
        function validateUrl(url) {
            try {
                new URL(url);
                return true;
            } catch (e) {
                return false;
            }
        }
    </script>
    
    <!-- Inject ads if configured -->
    {{ inject_before_body }}
</body>
</html>