<?php

return [
    // Server information
    'server' => [
        'name' => 'Proxy Server',
        'base_url' => 'https://proxy.yourdomain.com',
        'version' => '1.0.0'
    ],

    // Default settings
    'defaults' => [
        'language' => 'en',
        'timeout' => 30,
        'max_file_size' => 10485760, // 10MB
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ],

    // Security settings
    'security' => [
        'enable_csrf' => true,
        'max_requests_per_minute' => 60,
        'block_user_agents' => [
            'bot',
            'crawler',
            'spider',
            'curl',
            'wget'
        ],
        'allowed_referers' => [],
        'blocked_ips' => [],
        'require_https' => true
    ],

    // Site filtering
    'filtering' => [
        'allowed_sites' => [
            // List of allowed sites (empty means allow all)
        ],
        'blocked_sites' => [
            'malicious-site.com',
            'phishing-site.com',
            'spam-site.com'
        ],
        'blocked_keywords' => [
            'malware',
            'virus',
            'phishing'
        ],
        'blocked_extensions' => [
            '.exe',
            '.bat',
            '.cmd',
            '.scr',
            '.pif'
        ]
    ],

    // Ad configuration
    'ads' => [
        'header' => '',
        'footer' => '',
        'sidebar' => '',
        'inject_before_body' => '',
        'inject_after_body' => ''
    ],

    // Supported languages
    'languages' => [
        'en' => 'English',
        'es' => 'Español',
        'fr' => 'Français',
        'de' => 'Deutsch',
        'it' => 'Italiano',
        'pt' => 'Português',
        'ru' => 'Русский',
        'zh' => '中文',
        'ja' => '日本語',
        'ko' => '한국어'
    ],

    // Proxy settings
    'proxy' => [
        'enable_cookies' => true,
        'enable_javascript' => true,
        'enable_forms' => true,
        'compress_output' => true,
        'strip_scripts' => false,
        'remove_scripts' => false
    ],

    // Error handling
    'error_handling' => [
        'show_errors' => false,
        'log_errors' => true,
        'error_log_file' => __DIR__ . '/../logs/error.log',
        'custom_error_pages' => true
    ],

    // Performance settings
    'performance' => [
        'enable_cache' => true,
        'cache_ttl' => 300, // 5 minutes
        'cache_dir' => __DIR__ . '/../cache',
        'enable_gzip' => true
    ],

    // Logging
    'logging' => [
        'enable_access_log' => true,
        'access_log_file' => __DIR__ . '/../logs/access.log',
        'log_user_agents' => false,
        'log_ips' => false // Set to false for privacy
    ]
];