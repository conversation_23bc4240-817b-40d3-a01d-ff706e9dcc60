<?php

return [
    // Main interface
    'site_title' => 'Web Proxy',
    'site_description' => 'Browse the web securely and anonymously',
    'enter_url' => 'Enter URL to visit:',
    'go_button' => 'Go',
    'browsing_via' => 'Browsing via {server_name}',
    
    // Navigation
    'nav_home' => 'Home',
    'nav_settings' => 'Settings',
    'nav_about' => 'About',
    'nav_contact' => 'Contact',
    
    // Error messages
    'error_invalid_url' => 'Invalid URL provided',
    'error_site_blocked' => 'This site has been blocked',
    'error_access_denied' => 'Access denied',
    'error_timeout' => 'Request timeout',
    'error_connection_failed' => 'Connection failed',
    'error_file_too_large' => 'File too large',
    'error_malicious_content' => 'Malicious content detected',
    'error_rate_limited' => 'Too many requests. Please try again later.',
    'error_server_error' => 'Internal server error',
    'error_maintenance' => 'Server under maintenance',
    
    // Status messages
    'status_loading' => 'Loading...',
    'status_connecting' => 'Connecting...',
    'status_processing' => 'Processing...',
    'status_complete' => 'Complete',
    
    // Settings
    'settings_title' => 'Proxy Settings',
    'setting_language' => 'Language',
    'setting_theme' => 'Theme',
    'setting_compression' => 'Enable compression',
    'setting_javascript' => 'Enable JavaScript',
    'setting_cookies' => 'Enable cookies',
    'setting_images' => 'Load images',
    'setting_remove_scripts' => 'Remove scripts',
    'save_settings' => 'Save Settings',
    'reset_settings' => 'Reset to Default',
    'settings_saved' => 'Settings saved successfully',
    
    // Form elements
    'form_submit' => 'Submit',
    'form_cancel' => 'Cancel',
    'form_reset' => 'Reset',
    'form_search' => 'Search',
    
    // Footer
    'footer_copyright' => '© {year} {server_name}. All rights reserved.',
    'footer_privacy' => 'Privacy Policy',
    'footer_terms' => 'Terms of Service',
    'footer_disclaimer' => 'This service is provided as-is. Use at your own risk.',
    
    // About page
    'about_title' => 'About This Proxy',
    'about_description' => 'This proxy service allows you to browse the web anonymously and securely.',
    'about_features' => 'Features:',
    'about_feature1' => 'Anonymous browsing',
    'about_feature2' => 'SSL encryption support',
    'about_feature3' => 'JavaScript and cookie support',
    'about_feature4' => 'Mobile-friendly interface',
    'about_disclaimer' => 'Disclaimer: This proxy service is provided for educational and legitimate purposes only. Users are responsible for their actions while using this service.',
    
    // Technical messages
    'tech_no_response' => 'No response from server',
    'tech_bad_request' => 'Bad request',
    'tech_forbidden' => 'Access forbidden',
    'tech_not_found' => 'Page not found',
    'tech_internal_error' => 'Internal server error',
    'tech_bad_gateway' => 'Bad gateway',
    'tech_service_unavailable' => 'Service unavailable',
    'tech_gateway_timeout' => 'Gateway timeout',
    
    // Security messages
    'security_blocked_ip' => 'Your IP address has been blocked',
    'security_blocked_agent' => 'Your user agent is blocked',
    'security_invalid_referer' => 'Invalid referer',
    'security_csrf_failed' => 'Security validation failed',
    'security_https_required' => 'HTTPS connection required',
    
    // File handling
    'file_download' => 'Download File',
    'file_type_blocked' => 'This file type is blocked',
    'file_size_exceeded' => 'File size limit exceeded',
    'file_scan_failed' => 'File scan failed',
    
    // Statistics
    'stats_title' => 'Proxy Statistics',
    'stats_requests' => 'Total Requests',
    'stats_bandwidth' => 'Bandwidth Used',
    'stats_uptime' => 'Server Uptime',
    'stats_cached' => 'Cached Requests',
    
    // Contact
    'contact_title' => 'Contact Us',
    'contact_name' => 'Your Name',
    'contact_email' => 'Your Email',
    'contact_message' => 'Message',
    'contact_send' => 'Send Message',
    'contact_success' => 'Message sent successfully',
    'contact_error' => 'Error sending message'
];