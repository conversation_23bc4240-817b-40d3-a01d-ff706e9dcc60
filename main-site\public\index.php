<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Twig\Environment;
use Twig\Loader\FilesystemLoader;

// Load configuration
$config = require __DIR__ . '/../src/Config/config.php';
$servers = require __DIR__ . '/../src/Servers/servers.php';

// Initialize Twig
$loader = new FilesystemLoader(__DIR__ . '/../src/Templates');
$twig = new Environment($loader, [
    'cache' => false, // Set to a path for production
    'debug' => true,
]);

// Get current language
$currentLang = $_GET['lang'] ?? $_COOKIE['proxy_lang'] ?? $config['default_language'];
$currentLang = in_array($currentLang, array_keys($config['languages'])) ? $currentLang : $config['default_language'];

// Set language cookie
setcookie('proxy_lang', $currentLang, time() + (86400 * 30), '/');

// Load language file
$langFile = __DIR__ . '/../lang/' . $currentLang . '.php';
$translations = file_exists($langFile) ? require $langFile : require __DIR__ . '/../lang/en.php';

// Security functions
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL);
}

// Basic security check
function checkSecurity($config) {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    foreach ($config['security']['block_user_agents'] as $blocked) {
        if (stripos($userAgent, $blocked) !== false) {
            http_response_code(403);
            die('Access denied');
        }
    }
}

checkSecurity($config);

// Prepare template data
$templateData = [
    'lang' => $currentLang,
    'site_title' => $config['site']['title'],
    'site_description' => $config['site']['description'],
    'site_keywords' => $config['site']['keywords'],
    'current_language' => $config['languages'][$currentLang] ?? 'English',
    'languages' => $config['languages'],
    'header_ads' => $config['ads']['header'],
    'footer_ads' => $config['ads']['footer'],
    'custom_css' => $config['theme']['custom_css'],
    'servers' => $servers,
    // Add all translations
    ...$translations
];

// Handle different pages
$page = $_GET['page'] ?? 'home';

switch ($page) {
    case 'home':
    default:
        $templateData['content'] = renderServerList($servers, $translations);
        break;
        
    case 'server':
        $serverId = sanitizeInput($_GET['id'] ?? '');
        $server = findServerById($servers, $serverId);
        
        if ($server) {
            $templateData['content'] = renderServerDetails($server, $translations);
        } else {
            $templateData['content'] = '<div class="alert alert-danger">' . $translations['error_server_not_found'] . '</div>';
        }
        break;
        
    case 'settings':
        $templateData['content'] = renderSettings($config, $currentLang, $translations);
        break;
}

// Render the page
echo $twig->render('template.php', $templateData);

// Helper functions
function renderServerList($servers, $translations) {
    if (empty($servers)) {
        return '<div class="alert alert-warning">' . $translations['error_no_servers'] . '</div>';
    }
    
    $html = '<section id="servers">
        <h2 class="text-center mb-5">' . $translations['select_server'] . '</h2>
        <div class="row g-4">';
    
    foreach ($servers as $server) {
        $statusClass = getStatusClass($server['status']);
        $loadClass = getLoadClass($server['load']);
        $disabled = $server['status'] !== 'online' ? 'disabled' : '';
        
        $html .= '<div class="col-md-6 col-lg-3">
            <div class="card server-card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <span style="font-size: 2rem;">' . ($server['flag'] ?? '🌐') . '</span>
                    </div>
                    <h5 class="card-title">' . sanitizeInput($server['name']) . '</h5>
                    <p class="text-muted small">' . sanitizeInput($server['location']) . '</p>
                    
                    <div class="mb-3">
                        <span class="badge ' . $statusClass . '">' . $translations['status_' . $server['status']] . '</span>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">' . $translations['server_load'] . ': ' . $server['load'] . '%</small>
                        <div class="load-bar mt-1">
                            <div class="load-fill ' . $loadClass . '" style="width: ' . $server['load'] . '%"></div>
                        </div>
                    </div>
                    
                    <p class="card-text small">' . sanitizeInput($server['description']) . '</p>
                    
                    <button class="btn btn-primary connect-btn w-100" ' . $disabled . ' 
                            data-url="' . sanitizeInput($server['url']) . '">
                        <i class="bi bi-arrow-right-circle"></i> ' . $translations['connect_button'] . '
                    </button>
                </div>
            </div>
        </div>';
    }
    
    $html .= '</div></section>';
    return $html;
}

function renderServerDetails($server, $translations) {
    $statusClass = getStatusClass($server['status']);
    $loadClass = getLoadClass($server['load']);
    
    return '<div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <div class="text-center mb-4">
                        <span style="font-size: 4rem;">' . ($server['flag'] ?? '🌐') . '</span>
                        <h2 class="mt-3">' . sanitizeInput($server['name']) . '</h2>
                        <p class="text-muted">' . sanitizeInput($server['location']) . '</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>' . $translations['server_status'] . '</h5>
                            <span class="badge ' . $statusClass . '">' . $translations['status_' . $server['status']] . '</span>
                        </div>
                        <div class="col-md-6">
                            <h5>' . $translations['server_load'] . '</h5>
                            <div class="load-bar">
                                <div class="load-fill ' . $loadClass . '" style="width: ' . $server['load'] . '%"></div>
                            </div>
                            <small>' . $server['load'] . '%</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h5>' . $translations['server_description'] . '</h5>
                    <p>' . sanitizeInput($server['description']) . '</p>
                    
                    <div class="text-center mt-4">
                        <button class="btn btn-primary btn-lg connect-btn" 
                                data-url="' . sanitizeInput($server['url']) . '"
                                ' . ($server['status'] !== 'online' ? 'disabled' : '') . '>
                            <i class="bi bi-arrow-right-circle"></i> ' . $translations['connect_button'] . '
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>';
}

function renderSettings($config, $currentLang, $translations) {
    $html = '<div class="row">
        <div class="col-lg-6 mx-auto">
            <div class="card">
                <div class="card-body">
                    <h3 class="card-title text-center mb-4">' . $translations['settings_title'] . '</h3>
                    
                    <form id="settingsForm" method="post">
                        <div class="mb-3">
                            <label for="language" class="form-label">' . $translations['language_select'] . '</label>
                            <select class="form-select" id="language" name="language">
                                <option value="">Select Language</option>';
    
    foreach ($config['languages'] as $code => $name) {
        $selected = ($code == $currentLang) ? 'selected' : '';
        $html .= '<option value="' . $code . '" ' . $selected . '>' . $name . '</option>';
    }
    
    $html .= '</select>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> ' . $translations['save_settings'] . '
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>';
    
    return $html;
}

function findServerById($servers, $id) {
    foreach ($servers as $server) {
        if ($server['id'] === $id) {
            return $server;
        }
    }
    return null;
}

function getStatusClass($status) {
    switch ($status) {
        case 'online': return 'bg-success';
        case 'offline': return 'bg-danger';
        case 'maintenance': return 'bg-warning';
        default: return 'bg-secondary';
    }
}

function getLoadClass($load) {
    if ($load < 50) return 'load-low';
    if ($load < 80) return 'load-medium';
    return 'load-high';
}