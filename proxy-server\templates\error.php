<!DOCTYPE html>
<html lang="{{ lang }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ error_title }} - {{ site_title }}</title>
    <meta name="description" content="{{ error_message }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <style>
        .error-page {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .error-container {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            margin: 2rem;
        }
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-code {
            font-size: 3rem;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }
        .error-message {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .error-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .language-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Language Selector -->
    <div class="language-selector">
        <div class="dropdown">
            <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-globe"></i> {{ current_language }}
            </button>
            <ul class="dropdown-menu">
                {% for code, name in languages %}
                <li><a class="dropdown-item" href="?lang={{ code }}">{{ name }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Error Page -->
    <div class="error-page">
        <div class="error-container text-center">
            <div class="error-icon">
                <i class="bi bi-exclamation-triangle-fill"></i>
            </div>
            
            <div class="error-code">{{ error_code }}</div>
            <h1 class="error-title">{{ error_title }}</h1>
            <p class="error-message">{{ error_message }}</p>
            
            {% if error_details %}
            <div class="alert alert-warning">
                <i class="bi bi-info-circle"></i> {{ error_details }}
            </div>
            {% endif %}
            
            <div class="error-actions justify-content-center">
                <a href="/" class="btn btn-primary">
                    <i class="bi bi-house-door"></i> {{ nav_home }}
                </a>
                <button onclick="window.history.back()" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> {{ go_back }}
                </button>
                <a href="/?page=settings" class="btn btn-outline-info">
                    <i class="bi bi-gear"></i> {{ nav_settings }}
                </a>
            </div>
            
            {% if show_debug %}
            <div class="mt-4 text-start">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <pre class="small">{{ debug_info }}</pre>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-redirect for maintenance mode
        {% if auto_redirect %}
        setTimeout(function() {
            window.location.href = "{{ auto_redirect }}";
        }, 5000);
        {% endif %}
        
        // Error tracking
        document.addEventListener('DOMContentLoaded', function() {
            // Log error for analytics (if configured)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'page_view', {
                    'event_category': 'error',
                    'event_label': '{{ error_code }}',
                    'page_path': window.location.pathname
                });
            }
        });
    </script>
</body>
</html>