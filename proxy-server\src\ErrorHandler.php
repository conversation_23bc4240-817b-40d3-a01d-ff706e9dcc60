<?php

namespace App;

class ErrorHandler {
    private $config;
    private $translations;
    
    public function __construct($config, $translations) {
        $this->config = $config;
        $this->translations = $translations;
    }
    
    public function handleError($errno, $errstr, $errfile, $errline) {
        if (!(error_reporting() & $errno)) {
            return false;
        }
        
        $errorType = $this->getErrorType($errno);
        $message = "{$errorType}: {$errstr} in {$errfile} on line {$errline}";
        
        if ($this->config['error_handling']['log_errors']) {
            $this->logError($message);
        }
        
        if ($this->config['error_handling']['show_errors']) {
            $this->displayError($message, $errno);
        }
        
        return true;
    }
    
    public function handleException($exception) {
        $message = get_class($exception) . ": " . $exception->getMessage() . 
                  " in " . $exception->getFile() . " on line " . $exception->getLine();
        
        if ($this->config['error_handling']['log_errors']) {
            $this->logError($message);
        }
        
        $this->displayError($exception->getMessage(), $exception->getCode());
    }
    
    public function handleFatalError() {
        $error = error_get_last();
        if ($error !== null && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            $message = "Fatal Error: {$error['message']} in {$error['file']} on line {$error['line']}";
            
            if ($this->config['error_handling']['log_errors']) {
                $this->logError($message);
            }
            
            $this->displayError($error['message'], $error['type']);
        }
    }
    
    private function getErrorType($errno) {
        switch ($errno) {
            case E_ERROR:
                return 'Error';
            case E_WARNING:
                return 'Warning';
            case E_PARSE:
                return 'Parse Error';
            case E_NOTICE:
                return 'Notice';
            case E_CORE_ERROR:
                return 'Core Error';
            case E_CORE_WARNING:
                return 'Core Warning';
            case E_COMPILE_ERROR:
                return 'Compile Error';
            case E_COMPILE_WARNING:
                return 'Compile Warning';
            case E_USER_ERROR:
                return 'User Error';
            case E_USER_WARNING:
                return 'User Warning';
            case E_USER_NOTICE:
                return 'User Notice';
            case E_STRICT:
                return 'Strict';
            case E_RECOVERABLE_ERROR:
                return 'Recoverable Error';
            case E_DEPRECATED:
                return 'Deprecated';
            case E_USER_DEPRECATED:
                return 'User Deprecated';
            default:
                return 'Unknown Error';
        }
    }
    
    private function logError($message) {
        $logFile = $this->config['error_handling']['error_log_file'];
        
        // Create log directory if it doesn't exist
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $logMessage = "[{$timestamp}] [{$ip}] [{$requestUri}] {$message}\n";
        
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }
    
    private function displayError($message, $code = 500) {
        if ($this->config['error_handling']['custom_error_pages']) {
            $this->displayCustomErrorPage($message, $code);
        } else {
            $this->displaySimpleError($message, $code);
        }
    }
    
    private function displayCustomErrorPage($message, $code) {
        try {
            $loader = new \Twig\Loader\FilesystemLoader(__DIR__ . '/../templates');
            $twig = new \Twig\Environment($loader, [
                'cache' => false,
                'debug' => true,
            ]);
            
            $errorTitle = $this->getErrorTitle($code);
            
            echo $twig->render('error.php', [
                'error_code' => $code,
                'error_title' => $errorTitle,
                'error_message' => $message,
                'error_details' => $this->config['error_handling']['show_errors'] ? debug_backtrace() : '',
                'show_debug' => $this->config['error_handling']['show_errors'],
                'debug_info' => json_encode(debug_backtrace(), JSON_PRETTY_PRINT),
                'current_language' => $this->getCurrentLanguage(),
                'languages' => $this->config['languages'],
                'lang' => $this->getCurrentLanguageCode(),
                'site_title' => $this->config['server']['name'],
                'auto_redirect' => '/',
                // Translations
                ...$this->translations
            ]);
        } catch (\Exception $e) {
            $this->displaySimpleError($message, $code);
        }
    }
    
    private function displaySimpleError($message, $code) {
        http_response_code($code);
        echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Error {$code}</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .error-container { background: white; padding: 40px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
        .error-code { font-size: 4rem; color: #dc3545; margin-bottom: 20px; }
        .error-message { color: #666; margin-bottom: 30px; }
        .back-link { color: #007bff; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class='error-container'>
        <div class='error-code'>{$code}</div>
        <h1>Error</h1>
        <p class='error-message'>" . htmlspecialchars($message) . "</p>
        <a href='/' class='back-link'>← Back to Home</a>
    </div>
</body>
</html>";
    }
    
    private function getErrorTitle($code) {
        switch ($code) {
            case 400:
                return 'Bad Request';
            case 401:
                return 'Unauthorized';
            case 403:
                return 'Forbidden';
            case 404:
                return 'Not Found';
            case 405:
                return 'Method Not Allowed';
            case 408:
                return 'Request Timeout';
            case 413:
                return 'Payload Too Large';
            case 414:
                return 'URI Too Long';
            case 429:
                return 'Too Many Requests';
            case 500:
                return 'Internal Server Error';
            case 501:
                return 'Not Implemented';
            case 502:
                return 'Bad Gateway';
            case 503:
                return 'Service Unavailable';
            case 504:
                return 'Gateway Timeout';
            default:
                return 'Error';
        }
    }
    
    private function getCurrentLanguage() {
        return $this->config['languages'][$this->getCurrentLanguageCode()] ?? 'English';
    }
    
    private function getCurrentLanguageCode() {
        return $_GET['lang'] ?? $_COOKIE['proxy_lang'] ?? $this->config['defaults']['language'];
    }
    
    public function registerHandlers() {
        // Register error handler
        set_error_handler([$this, 'handleError']);
        
        // Register exception handler
        set_exception_handler([$this, 'handleException']);
        
        // Register fatal error handler
        register_shutdown_function([$this, 'handleFatalError']);
    }
}