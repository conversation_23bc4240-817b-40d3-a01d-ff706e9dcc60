<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\ProxyHandler;
use App\ErrorHandler;

// Load configuration
$config = require __DIR__ . '/../config/config.php';

// Set error reporting
error_reporting($config['error_handling']['show_errors'] ? E_ALL : 0);
ini_set('display_errors', $config['error_handling']['show_errors'] ? '1' : '0');

// Get current language
$currentLang = $_GET['lang'] ?? $_COOKIE['proxy_lang'] ?? $config['defaults']['language'];
$currentLang = in_array($currentLang, array_keys($config['languages'])) ? $currentLang : $config['defaults']['language'];

// Set language cookie
setcookie('proxy_lang', $currentLang, time() + (86400 * 30), '/');

// Load language file
$langFile = __DIR__ . '/../lang/' . $currentLang . '.php';
$translations = file_exists($langFile) ? require $langFile : require __DIR__ . '/../lang/en.php';

// Initialize error handler
$errorHandler = new ErrorHandler($config, $translations);
$errorHandler->registerHandlers();

// Initialize proxy handler
$proxyHandler = new ProxyHandler($config, $translations);

// Handle the request
try {
    $response = $proxyHandler->handle();
    
    // Output the response
    if ($response instanceof \Psr\Http\Message\ResponseInterface) {
        // Set headers
        foreach ($response->getHeaders() as $name => $values) {
            foreach ($values as $value) {
                header(sprintf('%s: %s', $name, $value), false);
            }
        }
        
        // Set status code
        http_response_code($response->getStatusCode());
        
        // Output body
        echo $response->getBody();
    } else {
        // Direct HTML output
        echo $response;
    }
    
} catch (\Exception $e) {
    // This should be caught by the error handler, but just in case
    $errorHandler->handleException($e);
}

// Log access if enabled
if ($config['logging']['enable_access_log']) {
    logAccess($config);
}

// Helper functions
function logAccess($config) {
    $logFile = $config['logging']['access_log_file'];
    
    // Create log directory if it doesn't exist
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = $config['logging']['log_ips'] ? ($_SERVER['REMOTE_ADDR'] ?? 'unknown') : 'hidden';
    $method = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
    $uri = $_SERVER['REQUEST_URI'] ?? 'unknown';
    $userAgent = $config['logging']['log_user_agents'] ? ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown') : 'hidden';
    $referer = $_SERVER['HTTP_REFERER'] ?? '-';
    
    $logMessage = sprintf(
        "[%s] %s %s %s %s %s\n",
        $timestamp,
        $ip,
        $method,
        $uri,
        $referer,
        $userAgent
    );
    
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL);
}

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Remove PHP version header
header_remove('X-Powered-By');

// Content Security Policy (basic)
header("Content-Security-Policy: default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval'");