# PHP Proxy Script

This is a full-featured PHP proxy script that can be deployed on multiple servers to create a proxy network. It provides secure web browsing with advanced filtering and security features.

## Features

- **Full Proxy Functionality**: Supports GET and POST requests, HTTPS, cookies, and JavaScript
- **Security Features**: URL filtering, rate limiting, user agent blocking, IP blocking
- **Multi-language Support**: Complete translation system with multiple languages
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5
- **Ad Management**: Configurable ad placements throughout the interface
- **Error Handling**: Custom error pages with detailed error information
- **No Database**: Uses flat configuration files for easy setup
- **Logging**: Access and error logging capabilities
- **Performance**: Caching and compression support

## Installation

1. **Upload Files**: Upload all files to your proxy server's web root
2. **Install Dependencies**: Run `composer install` in the project root
3. **Configure**: Edit the configuration file in `config/config.php`
4. **Set Permissions**: Ensure the web server has write access to `logs/` and `cache/` directories
5. **Set Up SSL**: Install an SSL certificate (HTTPS is recommended)

## Configuration

### Main Configuration (`config/config.php`)

The configuration file contains all settings for the proxy server:

```php
return [
    'server' => [
        'name' => 'Proxy Server',
        'base_url' => 'https://proxy.yourdomain.com',
        'version' => '1.0.0'
    ],
    'security' => [
        'enable_csrf' => true,
        'max_requests_per_minute' => 60,
        'block_user_agents' => ['bot', 'crawler', 'spider'],
        'require_https' => true
    ],
    'filtering' => [
        'allowed_sites' => [],
        'blocked_sites' => ['malicious-site.com'],
        'blocked_keywords' => ['malware', 'virus'],
        'blocked_extensions' => ['.exe', '.bat']
    ],
    'ads' => [
        'header' => '',
        'footer' => '',
        'sidebar' => ''
    ]
];
```

### Key Configuration Options

#### Server Settings
- `name`: Display name of the proxy server
- `base_url`: The full URL where this proxy is hosted
- `version`: Version number for display purposes

#### Security Settings
- `enable_csrf`: Enable CSRF protection
- `max_requests_per_minute`: Rate limiting to prevent abuse
- `block_user_agents`: List of user agent keywords to block
- `require_https`: Force HTTPS connections

#### Site Filtering
- `allowed_sites`: List of allowed domains (empty = allow all)
- `blocked_sites`: List of blocked domains
- `blocked_keywords`: Keywords that trigger blocking
- `blocked_extensions`: File extensions to block

#### Ad Management
- `header`: HTML/JS code for header ads
- `footer`: HTML/JS code for footer ads
- `sidebar`: HTML/JS code for sidebar ads
- `inject_before_body`: Code to inject before </body>
- `inject_after_body`: Code to inject after <body>

## Usage

1. **Direct Access**: Users can visit the proxy URL directly
2. **From Main Site**: Users can be redirected from the main proxy listing site
3. **URL Format**: `https://proxy.yourdomain.com/?url=https://example.com`

### Example Usage

```bash
# Browse a website through the proxy
https://proxy.yourdomain.com/?url=https://example.com

# With language parameter
https://proxy.yourdomain.com/?url=https://example.com&lang=es
```

## Security Considerations

### Built-in Security Features
- **URL Validation**: All URLs are validated and sanitized
- **Content Filtering**: Blocks malicious content and file types
- **Rate Limiting**: Prevents abuse through request limits
- **User Agent Filtering**: Blocks bots and crawlers
- **HTTPS Enforcement**: Optional HTTPS requirement
- **Security Headers**: Includes security headers like CSP and XSS protection

### Recommended Security Practices
1. **Use HTTPS**: Always deploy with SSL/TLS
2. **Regular Updates**: Keep dependencies updated
3. **Monitor Logs**: Regularly check access and error logs
4. **IP Whitelisting**: Consider restricting access to specific IPs
5. **Regular Audits**: Review configuration and blocked sites

## Adding Languages

1. Create a new language file in `lang/` directory (e.g., `fr.php`)
2. Copy the structure from `en.php` and translate all strings
3. Add the language to the configuration file
4. Test the translation thoroughly

## Performance Optimization

### Caching
- Enable caching in configuration
- Set appropriate cache TTL
- Ensure cache directory is writable

### Compression
- Enable gzip compression
- Configure web server compression
- Optimize image and asset delivery

### Server Configuration
- Use PHP 7.4 or higher
- Enable OPcache
- Configure appropriate memory limits
- Use a web server like Nginx or Apache

## File Structure

```
php-proxy-script/
├── composer.json           # Composer dependencies
├── public/
│   └── index.php          # Main entry point
├── config/
│   └── config.php         # Main configuration
├── src/
│   ├── Proxy.php          # Main proxy handler
│   └── ErrorHandler.php   # Error handling
├── lang/
│   ├── en.php            # English translations
│   └── es.php            # Spanish translations
├── templates/
│   ├── proxy.php         # Main proxy template
│   └── error.php         # Error template
├── logs/                  # Log files (created automatically)
├── cache/                 # Cache files (created automatically)
└── README.md             # This file
```

## Troubleshooting

### Common Issues

**Permission Errors**
- Ensure web server has write access to `logs/` and `cache/` directories
- Check file permissions (755 for directories, 644 for files)

**SSL/HTTPS Issues**
- Ensure SSL certificate is properly installed
- Check that `require_https` is set correctly in config
- Verify server SSL configuration

**Performance Issues**
- Enable caching in configuration
- Check server resources (CPU, memory)
- Monitor error logs for issues

**Blocked Content**
- Review blocked sites and keywords lists
- Check URL filtering rules
- Verify content type handling

### Debug Mode

To enable debug mode:
1. Set `show_errors` to `true` in configuration
2. Check error logs for detailed information
3. Use browser developer tools for network issues

## Requirements

- PHP 7.4 or higher
- Composer
- SSL Certificate (recommended)
- Web server (Apache/Nginx)
- PHP Extensions: cURL, JSON, OpenSSL
- Write permissions for logs and cache directories

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review error logs
3. Consult the project documentation
4. Create an issue in the project repository

## License

This project is provided as-is for educational and legitimate purposes. Users are responsible for ensuring compliance with applicable laws and regulations.